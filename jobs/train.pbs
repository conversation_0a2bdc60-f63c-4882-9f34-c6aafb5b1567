#!/bin/sh

#PBS -q normal
#PBS -l select=1:ngpus=4
#PBS -l walltime=24:00:00
#PBS -P 12004256
###PBS -P personal-s240076
#PBS -N GCNN-Shastry
#PBS -j oe

# 进入工作目录
cd $PBS_O_WORKDIR || exit $?

# 记录作业开始时间和节点信息
echo "Job start at: $(date)"
echo "Running on node: $(hostname)"
echo "GPU Information:"
nvidia-smi

# 加载必要的模块
module load singularity

# 从training_config.py读取参数列表
echo "Reading parameters from training_config.py..."
echo "当前工作目录: $(pwd)"
echo "列出configs目录内容:"
ls -la configs/

# 直接设置参数值，避免使用singularity读取配置
L_VALUES="6"
J2_VALUES="0.00"
J1_VALUES="0.02 0.07"

echo "手动设置参数值:"
echo "L_VALUES = $L_VALUES"
echo "J2_VALUES = $J2_VALUES"
echo "J1_VALUES = $J1_VALUES"

echo "Processing the following parameter combinations:"
echo "L values: $L_VALUES"
echo "J2 values: $J2_VALUES"
echo "J1 values: $J1_VALUES"

# 并行任务最大数量
max_tasks=1
current_tasks=0

for L in $L_VALUES; do
    for J2 in $J2_VALUES; do
        for J1 in $J1_VALUES; do
            echo "Starting computation L=$L, J2=$J2, J1=$J1 at: $(date)"

            # 提交任务到后台运行
            singularity exec --nv -B /scratch,/app \
                /home/<USER>/ntu/s240076/Repositories/Jupyter_server/config/netket.sif \
                python scripts/train.py $L $J2 $J1 &

            current_tasks=$((current_tasks + 1))

            # 如果达到最大并行任务数，则等待这批任务全部结束，再继续提交
            if [ $current_tasks -ge $max_tasks ]; then
                wait
                current_tasks=0
            fi

            echo "Submitted job L=$L, J2=$J2, J1=$J1 at: $(date)"
        done
    done
done

# 等待剩余任务
wait

echo "Job finished at: $(date)"
