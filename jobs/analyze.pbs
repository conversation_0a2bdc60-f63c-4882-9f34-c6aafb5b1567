#!/bin/sh

#PBS -q normal
#PBS -l select=1:ngpus=1
#PBS -l walltime=4:00:00
###PBS -P 12004256
#PBS -P personal-s240076
#PBS -N analyze-Shastry
#PBS -j oe

# 进入工作目录
cd $PBS_O_WORKDIR || exit $?

# 记录作业开始时间和节点信息
echo "Analysis job start at: $(date)"
echo "Running on node: $(hostname)"
echo "GPU Information:"
nvidia-smi

# 加载必要的模块
module load singularity

# 开始分析

# 定义要分析的参数组合
# 格式: "L J2 J1"
PARAM_SETS=(
  "4 1.00 0.74"
)

# 并行任务最大数量
max_tasks=1
current_tasks=0

# 遍历所有参数组合并运行分析
for params in "${PARAM_SETS[@]}"; do
  # 提取参数
  read -r L J2 J1 <<< "$params"

  echo "Starting analysis: L=$L, J2=$J2, J1=$J1 at: $(date)"

  # 运行分析脚本（后台运行）
  singularity exec --nv -B /scratch,/app \
    /home/<USER>/ntu/s240076/Repositories/Jupyter_server/config/netket.sif \
    python scripts/analyze.py --L $L --J2 $J2 --J1 $J1 &

  current_tasks=$((current_tasks + 1))

  # 如果达到最大并行任务数，则等待这批任务全部结束，再继续提交
  if [ $current_tasks -ge $max_tasks ]; then
    wait
    current_tasks=0
    echo "Batch of analysis tasks completed at: $(date)"
  fi

  echo "Submitted analysis job: L=$L, J2=$J2, J1=$J1"
  echo "---------------------------------------"
done

# 等待剩余任务
wait

# 整理结果
echo "Organizing results..."

# 记录磁盘使用情况
echo "Disk usage for results:"
du -sh results/

echo "Job finished at: $(date)"
