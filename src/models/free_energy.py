import time
import jax
import jax.numpy as jnp
from jax import tree_util
from netket.experimental.driver.vmc_srt import VMC_SRt
from src.utils.logging import log_message

# 定义熵梯度计算函数
def T_logp2(params, inputs, temperature, model_instance):
    variables = {"params": params}
    preds = model_instance.apply(variables, inputs)
    return 2.0 * temperature * jnp.mean(jnp.real(preds)**2)

def T_logp_2(params, inputs, temperature, model_instance):
    variables = {"params": params}
    preds = model_instance.apply(variables, inputs)
    return 2.0 * temperature * (jnp.mean(jnp.real(preds)))**2

def clip_gradients(grad, max_norm):
    # 计算所有梯度的L2范数
    total_norm = jnp.sqrt(sum([jnp.sum(jnp.square(x)) for x in jax.tree_leaves(grad)]))
    # 如果总范数超过max_norm，则计算缩放因子，否则为1.0
    clip_coef = jnp.where(total_norm > max_norm, max_norm / (total_norm + 1e-6), 1.0)
    # 对所有梯度乘以缩放因子
    return jax.tree_map(lambda x: x * clip_coef, grad)

# 在你的训练步长中，例如在 _step_with_state 中修改梯度更新部分：
class FreeEnergyVMC_SRt(VMC_SRt):
    def __init__(self, temperature, clip_norm=0.5, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.init_temperature = temperature
        self.temperature = temperature
        self.clip_norm = clip_norm  # 设置梯度裁剪的阈值

    def _step_with_state(self, state):
        new_state = super()._step_with_state(state)
        params = new_state.parameters
        inputs = new_state.samples

        # 计算熵梯度部分（同之前代码）
        mT_grad_S_1 = jax.grad(T_logp2, argnums=0)(params, inputs, self.temperature, self.variational_state.model)
        mT_grad_S_2 = jax.grad(T_logp_2, argnums=0)(params, inputs, self.temperature, self.variational_state.model)
        mT_grad_S = tree_util.tree_map(lambda x, y: x - y, mT_grad_S_1, mT_grad_S_2)
        
        total_grad = tree_util.tree_map(lambda g_e, g_s: g_e - g_s,
                                          new_state.gradient, mT_grad_S)
        
        # # 对梯度进行裁剪
        # # 在FreeEnergyVMC_SRt类中加强梯度裁剪
        # total_grad = tree_util.tree_map(lambda g_e, g_s: jnp.clip(g_e - g_s, -self.clip_norm, self.clip_norm),
        #                       new_state.gradient, mT_grad_S)


        new_params = self.optimizer.update(total_grad, params)
        new_state = new_state.replace(parameters=new_params)
        return new_state

# 添加进度条以及温度递减方案
class CustomFreeEnergyVMC_SRt(FreeEnergyVMC_SRt):
    def __init__(self, reference_energy=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.reference_energy = reference_energy

    def run(self, n_iter, energy_log):
        """
        运行优化，通过 log_message 打印 Temperature, Learning Rate, Energy, 
        E_var, E_err，以及当提供 reference_energy 时显示的 Rel_err(%)
        增加了 NaN 检测机制，当检测到 energy 出现 nan 时自动停止训练
        """
        nan_count = 0
        max_nan_tolerance = 3  # 允许连续出现的 NaN 次数
        
        for i in range(n_iter):
            # 更新温度（以及其他内部状态）
            self.temperature = self.init_temperature * (jnp.exp(-i / 50.0) / 2.0)
            self.advance(1)

            energy_mean = self.energy.mean
            energy_var = self.energy.variance
            energy_error = self.energy.error_of_mean

            # 检测 NaN 值
            if jnp.isnan(energy_mean) or jnp.isinf(energy_mean):
                nan_count += 1
                log_message(energy_log, f"WARNING: Iteration {i}/{n_iter} - NaN/Inf detected in energy! (Count: {nan_count}/{max_nan_tolerance})")
                
                if nan_count >= max_nan_tolerance:
                    log_message(energy_log, f"ERROR: Training terminated at iteration {i}/{n_iter} due to persistent NaN/Inf values in energy")
                    log_message(energy_log, "Training stopped early to prevent resource waste")
                    # 直接退出程序
                    import sys
                    sys.exit(1)
            else:
                # 如果这次不是 NaN，重置计数器
                nan_count = 0

            if self.reference_energy is not None:
                relative_error = abs((energy_mean - self.reference_energy) / self.reference_energy) * 100
                log_message(
                    energy_log,
                    f"Iteration: {i}/{n_iter}, Temp: {self.temperature:.4f}, Energy: {energy_mean:.6f}, Rel_err(%): {relative_error:.4f}"
                )
            else:
                log_message(
                    energy_log,
                    f"Iteration: {i}/{n_iter}, Temp: {self.temperature:.4f}, Energy: {energy_mean:.6f}"
                )
        return self
