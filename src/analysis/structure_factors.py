import numpy as np
import netket as nk
import os
import jax
import jax.numpy as jnp
from functools import partial
from jax import vmap
from src.utils.logging import log_message

def create_data_structure(correlation_data, k_points_x, k_points_y, 
                                   structure_factor_complex, lattice, 
                                   calculation_type, reference_info=None):
    """
    创建优化的数据结构
    
    参数:
    - correlation_data: 原始相关函数数据列表
    - k_points_x, k_points_y: k点坐标
    - structure_factor_complex: 复数结构因子
    - lattice: 晶格对象
    - calculation_type: 计算类型 ('spin', 'dimer', 'diag_dimer', 'plaquette')
    - reference_info: 参考点信息
    
    返回:
    - 优化的数据结构字典
    """
    import time
    
    # 提取相关函数数据
    n_pairs = len(correlation_data)
    positions = np.zeros((n_pairs, 2))
    values = np.zeros(n_pairs, dtype=complex)
    errors = np.zeros(n_pairs, dtype=complex)
    variances = np.zeros(n_pairs, dtype=complex)
    pair_indices = np.zeros((n_pairs, 2), dtype=int)
    
    # 根据计算类型提取不同的索引信息
    if calculation_type == 'spin':
        for i, data in enumerate(correlation_data):
            positions[i] = [data['r_x'], data['r_y']]
            values[i] = data['corr_full_real'] + 1j * data['corr_full_imag']
            errors[i] = data['error'] + 1j * data['error_imag']
            variances[i] = data['variance'] + 1j * data['variance_imag']
            pair_indices[i] = [data['i'], data['j']]
    
    elif calculation_type in ['dimer', 'diag_dimer']:
        directions = np.array([data['direction'] for data in correlation_data])
        for i, data in enumerate(correlation_data):
            positions[i] = [data['r_x'], data['r_y']]
            values[i] = data['corr_full_real'] + 1j * data['corr_full_imag']
            errors[i] = data['error'] + 1j * data['error_imag']
            variances[i] = data['variance'] + 1j * data['variance_imag']
            pair_indices[i] = [data['dimer_i_idx'], data['dimer_j_idx']]
    
    elif calculation_type == 'plaquette':
        for i, data in enumerate(correlation_data):
            positions[i] = [data['r_x'], data['r_y']]
            values[i] = data['corr_full_real'] + 1j * data['corr_full_imag']
            errors[i] = data['error'] + 1j * data['error_imag']
            variances[i] = data['variance'] + 1j * data['variance_imag']
            pair_indices[i] = [data['plaq_i_idx'], data['plaq_j_idx']]
    
    # 创建k点网格
    kx_grid, ky_grid = np.meshgrid(k_points_x, k_points_y)
    k_points_grid = np.stack([kx_grid, ky_grid], axis=2)
    
    # 构建优化的数据结构
    optimized_data = {
        'metadata': {
            'lattice_info': {
                'Lx': lattice.extent[0],
                'Ly': lattice.extent[1], 
                'N_sites': lattice.n_nodes
            },
            'k_grid': {
                'kx': k_points_x,
                'ky': k_points_y,
                'shape': structure_factor_complex.shape
            },
            'calculation_type': calculation_type,
            'reference_info': reference_info or {},
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        },
        'correlations': {
            'positions': positions,
            'values': values,
            'errors': errors,
            'variances': variances,
            'pair_indices': pair_indices
        },
        'structure_factor': {
            'values': structure_factor_complex,
            'k_points': k_points_grid,
            'normalization': 1.0 / lattice.n_nodes
        }
    }
    
    # 为二聚体类型添加方向信息
    if calculation_type in ['dimer', 'diag_dimer']:
        optimized_data['correlations']['directions'] = directions
    
    return optimized_data

def save_optimized_data(data, save_path):
    """
    保存优化的数据结构
    
    参数:
    - data: 优化的数据结构
    - save_path: 保存路径
    """
    np.save(save_path, data)
    
def load_optimized_data(load_path):
    """
    加载优化的数据结构
    
    参数:
    - load_path: 加载路径
    
    返回:
    - 优化的数据结构
    """
    return np.load(load_path, allow_pickle=True).item()

# NOTE: In the functions below that calculate structure factors,
# the 'correlation_data' dictionary stores the correlation values.
# However, the 'error', 'error_imag', 'variance', and 'variance_imag' fields
# within 'correlation_data', as well as the derived 'errors_of_full_corr'
# and 'errors_imag_of_full_corr' in the final saved output,
# pertain to the statistical uncertainty of the two-operator
# expectation value (e.g., <S_0·S_r>, <D_0·D_r>, or <O_0·O_r>).

def create_k_mesh(lattice):
    """
    创建k点网格，根据晶格尺寸设置点数，使用π/L*n的方式生成k点, 范围从-π到π

    参数:
    - lattice: 晶格对象，从中获取Lx和Ly

    返回:
    - k_points_x: x方向的k点
    - k_points_y: y方向的k点
    - kx, ky: 网格化的k点
    """

    # 从晶格获取尺寸
    Lx, Ly = lattice.extent

    # 生成k点，范围为[-π, π]，确保包含端点
    # 使用linspace生成均匀分布的点
    k_points_x = np.linspace(-np.pi, np.pi, 2 * Lx + 1, endpoint=True)
    k_points_y = np.linspace(-np.pi, np.pi, 2 * Ly + 1, endpoint=True)
    kx, ky = np.meshgrid(k_points_x, k_points_y)

    return k_points_x, k_points_y, kx, ky

########################################################
# 计算spin structure factor
########################################################
def calculate_spin_structure_factor(vqs, lattice, L, save_dir, log_file=None):
    """
    计算自旋结构因子，现在使用真正的并行采样优化
    将调用新的并行版本
    """
    return calculate_spin_structure_factor_parallel(vqs, lattice, L, save_dir, log_file)

def calculate_dimer_structure_factor(vqs, lattice, L, save_dir, log_file=None):
    """
    计算二聚体结构因子，现在使用真正的并行采样优化
    将调用新的并行版本
    """
    return calculate_dimer_structure_factor_parallel(vqs, lattice, L, save_dir, log_file)

########################################################
# 计算plaquette structure factor
########################################################
def construct_plaquette_permutation(hilbert, plaq_sites):
    """
    构建简盘循环置换操作符，使用自旋交换算符
    P = S_{1,2} S_{2,3} S_{3,4} 实现循环置换 (1,2,3,4) -> (4,1,2,3)
    P^-1 = S_{1,4} S_{4,3} S_{3,2} 实现逆循环置换 (1,2,3,4) -> (2,3,4,1)

    其中 S_{i,j} = (1/2 + 2S_i·S_j) 是交换算符
    """
    a, b, c, d = plaq_sites

    # 构建各个位点的自旋算符
    S_a = [nk.operator.spin.sigmax(hilbert, a) * 0.5,
           nk.operator.spin.sigmay(hilbert, a) * 0.5,
           nk.operator.spin.sigmaz(hilbert, a) * 0.5]

    S_b = [nk.operator.spin.sigmax(hilbert, b) * 0.5,
           nk.operator.spin.sigmay(hilbert, b) * 0.5,
           nk.operator.spin.sigmaz(hilbert, b) * 0.5]

    S_c = [nk.operator.spin.sigmax(hilbert, c) * 0.5,
           nk.operator.spin.sigmay(hilbert, c) * 0.5,
           nk.operator.spin.sigmaz(hilbert, c) * 0.5]

    S_d = [nk.operator.spin.sigmax(hilbert, d) * 0.5,
           nk.operator.spin.sigmay(hilbert, d) * 0.5,
           nk.operator.spin.sigmaz(hilbert, d) * 0.5]

    # 将所有操作符转换为JAX操作符
    S_a = [op.to_jax_operator() for op in S_a]
    S_b = [op.to_jax_operator() for op in S_b]
    S_c = [op.to_jax_operator() for op in S_c]
    S_d = [op.to_jax_operator() for op in S_d]

    # 构建交换算符 S_{i,j} = (1/2 + 2S_i·S_j)
    def exchange_op(S_i, S_j):
        # 计算 S_i·S_j = S^x_i·S^x_j + S^y_i·S^y_j + S^z_i·S^z_j
        SiSj = S_i[0] @ S_j[0] + S_i[1] @ S_j[1] + S_i[2] @ S_j[2]
        # 返回 1/2 + 2(S_i·S_j)
        constant_op = nk.operator.LocalOperator(hilbert, constant=0.5).to_jax_operator()
        return constant_op + 2.0 * SiSj

    # 构建正向循环置换 P = S_{1,2} S_{2,3} S_{3,4}
    S_ab = exchange_op(S_a, S_b)
    S_bc = exchange_op(S_b, S_c)
    S_cd = exchange_op(S_c, S_d)

    # 构建逆向循环置换 P^-1 = S_{1,4} S_{4,3} S_{3,2}
    S_ad = exchange_op(S_a, S_d)
    S_dc = exchange_op(S_d, S_c)
    S_cb = exchange_op(S_c, S_b)

    # 组合操作符：P = S_{a,b} S_{b,c} S_{c,d}
    P = S_ab @ S_bc @ S_cd

    # 组合操作符：P^-1 = S_{a,d} S_{d,c} S_{c,b}
    P_inv = S_ad @ S_dc @ S_cb

    return P, P_inv

########################################################
# 计算对角二聚体-二聚体结构因子
########################################################    
def calculate_diag_dimer_structure_factor(vqs, lattice, L, save_dir, log_file=None):
    """
    计算对角二聚体结构因子，现在使用真正的并行采样优化
    将调用新的并行版本
    """
    return calculate_diag_dimer_structure_factor_parallel(vqs, lattice, L, save_dir, log_file)

def calculate_diag_dimer_structure_factor_parallel(vqs, lattice, L, save_dir, log_file=None):
    """
    计算对角二聚体-二聚体结构因子，使用真正的并行采样优化版本
    一次生成样本，批量计算所有算符期望值
    """
    # Try to determine a specific log_file name from save_dir components
    temp_dir_parts = save_dir.split('/')
    for i, current_dir_part in enumerate(temp_dir_parts):
        if current_dir_part.startswith('J2='):
            if i + 1 < len(temp_dir_parts) and temp_dir_parts[i+1].startswith('J1='):
                temp_J2_str = current_dir_part.split('=')[1]
                temp_J1_str = temp_dir_parts[i+1].split('=')[1]
                log_file = os.path.join(os.path.dirname(save_dir), f"analyze_L={L}_J2={temp_J2_str}_J1={temp_J1_str}.log")
                break

    log_message(log_file, "开始计算对角二聚体结构因子（真正并行采样版本）...")

    # 获取总位点数
    N = lattice.n_nodes

    # 创建k点网格
    k_points_x, k_points_y, kx_grid, ky_grid = create_k_mesh(lattice)
    n_kx = len(k_points_x)
    n_ky = len(k_points_y)

    # 获取晶格尺寸
    Lx, Ly = lattice.extent

    # 识别对角二聚体
    log_message(log_file, "识别所有对角二聚体...")
    diag_dimers_nwse = []  # 西北-东南方向的对角二聚体
    diag_dimers_swne = []  # 西南-东北方向的对角二聚体
    diag_dimer_positions_nwse = []
    diag_dimer_positions_swne = []

    # 遍历所有节点，寻找对角二聚体
    for i in range(N):
        pos_i = np.array(lattice.positions[i])
        
        for j in range(N):
            if i == j:
                continue
                
            pos_j = np.array(lattice.positions[j])
            
            # 计算原始位移
            displacement = pos_j - pos_i
            
            # 考虑周期性边界条件，计算最短位移
            displacement_pbc = displacement.copy()
            
            # 处理x方向周期性
            if displacement[0] > Lx:
                displacement_pbc[0] -= 2 * Lx
            elif displacement[0] < -Lx:
                displacement_pbc[0] += 2 * Lx
                
            # 处理y方向周期性  
            if displacement[1] > Ly:
                displacement_pbc[1] -= 2 * Ly
            elif displacement[1] < -Ly:
                displacement_pbc[1] += 2 * Ly
            
            # 西北-东南方向：j在i的右下方（相对位移约为[1, -1]）
            if (abs(displacement_pbc[0] - 1.0) < 0.1 and 
                abs(displacement_pbc[1] + 1.0) < 0.1):
                if (i, j) not in diag_dimers_nwse and (j, i) not in diag_dimers_nwse:
                    diag_dimers_nwse.append((i, j))
                    dimer_center = 0.5 * (pos_i + pos_j)
                    diag_dimer_positions_nwse.append(dimer_center)
            
            # 西南-东北方向：j在i的右上方（相对位移约为[1, 1]）
            elif (abs(displacement_pbc[0] - 1.0) < 0.1 and 
                  abs(displacement_pbc[1] - 1.0) < 0.1):
                if (i, j) not in diag_dimers_swne and (j, i) not in diag_dimers_swne:
                    diag_dimers_swne.append((i, j))
                    dimer_center = 0.5 * (pos_i + pos_j)
                    diag_dimer_positions_swne.append(dimer_center)

    dimers = diag_dimers_nwse + diag_dimers_swne
    dimer_positions = diag_dimer_positions_nwse + diag_dimer_positions_swne
    n_dimers_nwse = len(diag_dimers_nwse)
    n_dimers_swne = len(diag_dimers_swne)

    if len(dimers) == 0:
        log_message(log_file, "警告: 没有找到对角二聚体！")
        return (k_points_x, k_points_y), (np.zeros((n_ky, n_kx)), np.zeros((n_ky, n_kx)))

    n_dimers = len(dimers)
    log_message(log_file, f"总共找到 {n_dimers_nwse} 个西北-东南方向对角二聚体和 {n_dimers_swne} 个西南-东北方向对角二聚体")

    log_message(log_file, "预计算自旋操作符...")
    spin_ops_components = []
    for i in range(lattice.n_nodes):
        sx_i = nk.operator.spin.sigmax(vqs.hilbert, i) * 0.5
        sy_i = nk.operator.spin.sigmay(vqs.hilbert, i) * 0.5
        sz_i = nk.operator.spin.sigmaz(vqs.hilbert, i) * 0.5
        sx_i_jax = sx_i.to_jax_operator()
        sy_i_jax = sy_i.to_jax_operator()
        sz_i_jax = sz_i.to_jax_operator()
        spin_ops_components.append((sx_i_jax, sy_i_jax, sz_i_jax))

    log_message(log_file, "预计算对角二聚体操作符 D_k = S_k1·S_k2 ...")
    dimer_ops_list = []
    for i, (i1, i2) in enumerate(dimers):
        sx_i1, sy_i1, sz_i1 = spin_ops_components[i1]
        sx_i2, sy_i2, sz_i2 = spin_ops_components[i2]
        S_i1_dot_S_i2 = sx_i1 @ sx_i2 + sy_i1 @ sy_i2 + sz_i1 @ sz_i2
        dimer_ops_list.append(S_i1_dot_S_i2)

    log_message(log_file, "计算位移向量...")
    dimer_positions_np = np.array(dimer_positions)
    r_vectors = np.zeros((n_dimers, n_dimers, 2))
    for i in range(n_dimers):
        r_vectors[i] = dimer_positions_np - dimer_positions_np[i]

    # 批量构建所有需要计算的操作符
    all_operators = []
    operator_info = []

    # 处理西北-东南方向的对角二聚体
    if n_dimers_nwse > 0:
        ref_dimer_idx_nwse = 0
        op_D_ref_nwse = dimer_ops_list[ref_dimer_idx_nwse]
        dimer_ref_tuple_nwse = dimers[ref_dimer_idx_nwse]
        log_message(log_file, f"准备西北-东南方向对角二聚体相关函数计算...")

        for j_local_idx in range(n_dimers_nwse):
            j_global_idx = j_local_idx
            op_Dj = dimer_ops_list[j_global_idx]
            combined_op = op_D_ref_nwse @ op_Dj
            all_operators.append(combined_op)
            operator_info.append({
                'type': 'diag_nwse',
                'ref_idx': ref_dimer_idx_nwse,
                'j_idx': j_global_idx,
                'ref_tuple': dimer_ref_tuple_nwse,
                'j_tuple': dimers[j_global_idx]
            })

    # 处理西南-东北方向的对角二聚体
    if n_dimers_swne > 0:
        ref_dimer_idx_swne = n_dimers_nwse
        op_D_ref_swne = dimer_ops_list[ref_dimer_idx_swne]
        dimer_ref_tuple_swne = dimers[ref_dimer_idx_swne]
        log_message(log_file, f"准备西南-东北方向对角二聚体相关函数计算...")

        for j_local_idx in range(n_dimers_swne):
            j_global_idx = n_dimers_nwse + j_local_idx
            op_Dj = dimer_ops_list[j_global_idx]
            combined_op = op_D_ref_swne @ op_Dj
            all_operators.append(combined_op)
            operator_info.append({
                'type': 'diag_swne',
                'ref_idx': ref_dimer_idx_swne,
                'j_idx': j_global_idx,
                'ref_tuple': dimer_ref_tuple_swne,
                'j_tuple': dimers[j_global_idx]
            })

    # 使用真正的并行采样批量计算所有操作符
    import time
    start_time = time.time()

    correlation_results = batch_expect_with_shared_samples(vqs, all_operators, log_file=log_file)

    end_time = time.time()
    log_message(log_file, f"对角二聚体相关函数计算完成，总耗时 {end_time - start_time:.2f} 秒")

    # 处理结果
    diag_dimer_correlation_data = []
    for idx, (corr_obj, info) in enumerate(zip(correlation_results, operator_info)):
        r_ref_j = r_vectors[info['ref_idx'], info['j_idx']]
        corr_full = corr_obj.mean

        direction = 'diag_nwse' if info['type'] == 'diag_nwse' else 'diag_swne'

        diag_dimer_correlation_data.append({
            'dimer_i_idx': info['ref_idx'],
            'dimer_j_idx': info['j_idx'],
            'dimer_i_sites': info['ref_tuple'],
            'dimer_j_sites': info['j_tuple'],
            'r_x': r_ref_j[0],
            'r_y': r_ref_j[1],
            'corr_full_real': corr_full.real,
            'corr_full_imag': corr_full.imag,
            'error': corr_obj.error_of_mean.real,
            'error_imag': corr_obj.error_of_mean.imag,
            'variance': corr_obj.variance.real,
            'variance_imag': corr_obj.variance.imag,
            'direction': direction
        })

    if not diag_dimer_correlation_data:
        log_message(log_file, "没有足够的数据点进行傅里叶变换。")
        return (k_points_x, k_points_y), (np.zeros((n_ky, n_kx)), np.zeros((n_ky, n_kx)))

    log_message(log_file, "计算傅里叶变换...")

    r_values = np.array([[data['r_x'], data['r_y']] for data in diag_dimer_correlation_data])
    corr_full_values = np.array([data['corr_full_real'] + 1j * data['corr_full_imag'] for data in diag_dimer_correlation_data])

    k_grid = np.stack([kx_grid.flatten(), ky_grid.flatten()], axis=1)
    r_values_jax = jnp.array(r_values)
    corr_full_values_jax = jnp.array(corr_full_values)
    k_grid_jax = jnp.array(k_grid)

    def compute_sf_for_k(k_vec):
        phases = jnp.exp(1j * jnp.dot(r_values_jax, k_vec))
        return jnp.sum(corr_full_values_jax * phases)

    compute_sf_vmap = vmap(compute_sf_for_k)
    sf_values = compute_sf_vmap(k_grid_jax)
    sf_values_2d = sf_values.reshape(n_ky, n_kx)
    diag_dimer_sf_complex = np.array(sf_values_2d)

    diag_dimer_sf_complex /= N

    # 保存优化的数据结构
    reference_info = {}
    if n_dimers_nwse > 0:
        reference_info['nwse_reference'] = {'dimer_idx': 0, 'sites': dimers[0]}
    if n_dimers_swne > 0:
        reference_info['swne_reference'] = {'dimer_idx': n_dimers_nwse, 'sites': dimers[n_dimers_nwse]}

    optimized_data = create_data_structure(
        correlation_data=diag_dimer_correlation_data,
        k_points_x=k_points_x,
        k_points_y=k_points_y,
        structure_factor_complex=diag_dimer_sf_complex,
        lattice=lattice,
        calculation_type='diag_dimer',
        reference_info=reference_info
    )
    save_optimized_data(optimized_data, os.path.join(save_dir, "diag_dimer_data.npy"))

    log_message(log_file, "对角二聚体结构因子计算完成（使用真正并行采样）")

    return (k_points_x, k_points_y), (diag_dimer_sf_complex.real, diag_dimer_sf_complex.imag)

########################################################
# 计算简盘结构因子
########################################################    
def calculate_plaquette_structure_factor(vqs, lattice, L, save_dir, log_file=None):
    """
    计算简盘结构因子，现在使用真正的并行采样优化
    将调用新的并行版本  
    """
    return calculate_plaquette_structure_factor_parallel(vqs, lattice, L, save_dir, log_file)

def calculate_plaquette_structure_factor_parallel(vqs, lattice, L, save_dir, log_file=None):
    """
    计算简盘结构因子，使用真正的并行采样优化版本
    一次生成样本，批量计算所有算符期望值
    """
    # Try to determine a specific log_file name from save_dir components
    temp_dir_parts = save_dir.split('/')
    for i, current_dir_part in enumerate(temp_dir_parts):
        if current_dir_part.startswith('J2='):
            if i + 1 < len(temp_dir_parts) and temp_dir_parts[i+1].startswith('J1='):
                temp_J2_str = current_dir_part.split('=')[1]
                temp_J1_str = temp_dir_parts[i+1].split('=')[1]
                log_file = os.path.join(os.path.dirname(save_dir), f"analyze_L={L}_J2={temp_J2_str}_J1={temp_J1_str}.log")
                break

    log_message(log_file, "开始计算简盘结构因子（真正并行采样版本）...")

    import time
    k_points_x, k_points_y, kx_grid, ky_grid = create_k_mesh(lattice)
    n_kx = len(k_points_x)
    n_ky = len(k_points_y)

    # 识别所有可能的4节点plaquette
    log_message(log_file, "识别所有可能的4节点简盘...")
    plaquettes = []
    plaquette_positions = []
    
    N = lattice.n_nodes
    positions = [np.array(lattice.positions[i]) for i in range(N)]
    
    # 获取晶格尺寸用于周期性边界条件
    Lx, Ly = lattice.extent
    
    # 每个格点作为简盘的左下角，寻找右边、上边、右上角的格点
    for i in range(N):
        pos_i = positions[i]
        
        # 寻找右邻居（x坐标增加约1，考虑周期性边界）
        right_neighbor = None
        for j in range(N):
            if j == i:
                continue
            pos_j = positions[j]
            
            # 计算位移，考虑周期性边界条件
            dx = pos_j[0] - pos_i[0]
            dy = pos_j[1] - pos_i[1]
            
            # 处理x方向周期性边界
            if dx > Lx:
                dx -= 2 * Lx
            elif dx < -Lx:
                dx += 2 * Lx
            
            # 处理y方向周期性边界
            if dy > Ly:
                dy -= 2 * Ly
            elif dy < -Ly:
                dy += 2 * Ly
            
            if abs(dx - 1.0) < 0.1 and abs(dy) < 0.1:
                right_neighbor = j
                break
        
        # 寻找上邻居（y坐标增加约1，考虑周期性边界）
        up_neighbor = None
        for j in range(N):
            if j == i:
                continue
            pos_j = positions[j]
            
            # 计算位移，考虑周期性边界条件
            dx = pos_j[0] - pos_i[0]
            dy = pos_j[1] - pos_i[1]
            
            # 处理x方向周期性边界
            if dx > Lx:
                dx -= 2 * Lx
            elif dx < -Lx:
                dx += 2 * Lx
            
            # 处理y方向周期性边界
            if dy > Ly:
                dy -= 2 * Ly
            elif dy < -Ly:
                dy += 2 * Ly
            
            if abs(dx) < 0.1 and abs(dy - 1.0) < 0.1:
                up_neighbor = j
                break
        
        # 寻找右上邻居（x和y坐标都增加约1，考虑周期性边界）
        right_up_neighbor = None
        for j in range(N):
            if j == i:
                continue
            pos_j = positions[j]
            
            # 计算位移，考虑周期性边界条件
            dx = pos_j[0] - pos_i[0]
            dy = pos_j[1] - pos_i[1]
            
            # 处理x方向周期性边界
            if dx > Lx:
                dx -= 2 * Lx
            elif dx < -Lx:
                dx += 2 * Lx
            
            # 处理y方向周期性边界
            if dy > Ly:
                dy -= 2 * Ly
            elif dy < -Ly:
                dy += 2 * Ly
            
            if abs(dx - 1.0) < 0.1 and abs(dy - 1.0) < 0.1:
                right_up_neighbor = j
                break
        
        # 如果找到了所有三个邻居，就组成一个plaquette
        if (right_neighbor is not None and 
            up_neighbor is not None and 
            right_up_neighbor is not None):
            
            plaq = [i, right_neighbor, right_up_neighbor, up_neighbor]  # 逆时针顺序
            plaquettes.append(plaq)
            
            # 计算plaquette中心位置
            plaq_positions = [positions[node] for node in plaq]
            center_x = np.mean([pos[0] for pos in plaq_positions])
            center_y = np.mean([pos[1] for pos in plaq_positions])
            plaquette_positions.append(np.array([center_x, center_y]))

    log_message(log_file, f"找到 {len(plaquettes)} 个有效的4节点简盘")
    
    if len(plaquettes) == 0:
        log_message(log_file, "警告: 没有找到有效的简盘！")
        return (k_points_x, k_points_y), (np.zeros((n_ky, n_kx)), np.zeros((n_ky, n_kx)))

    log_message(log_file, "预计算简盘操作符 P_k 和 P_k^-1...")
    plaquette_P_ops = []
    plaquette_Pinv_ops = []
    
    for i, plaq_sites in enumerate(plaquettes):
        P, P_inv = construct_plaquette_permutation(vqs.hilbert, plaq_sites)
        plaquette_P_ops.append(P)
        plaquette_Pinv_ops.append(P_inv)

    log_message(log_file, "计算位移向量...")
    plaquette_positions_np = np.array(plaquette_positions)
    n_plaq = len(plaquettes)
    r_vectors = np.zeros((n_plaq, n_plaq, 2))
    for i in range(n_plaq):
        r_vectors[i] = plaquette_positions_np - plaquette_positions_np[i]

    reference_plaq_idx = 0
    P_ref = plaquette_P_ops[reference_plaq_idx]
    Pinv_ref = plaquette_Pinv_ops[reference_plaq_idx]
    O_0_ref = P_ref + Pinv_ref

    # 批量构建所有需要计算的操作符
    all_operators = []
    log_message(log_file, "准备简盘相关函数计算...")
    for j_idx in range(n_plaq):
        P_j = plaquette_P_ops[j_idx]
        Pinv_j = plaquette_Pinv_ops[j_idx]
        O_r_j = P_j + Pinv_j
        operator_to_expect = O_r_j @ O_0_ref
        all_operators.append(operator_to_expect)

    # 使用真正的并行采样批量计算所有操作符
    start_time = time.time()

    correlation_results = batch_expect_with_shared_samples(vqs, all_operators, log_file=log_file)

    end_time = time.time()
    log_message(log_file, f"简盘相关函数计算完成，总耗时 {end_time - start_time:.2f} 秒")

    # 处理结果
    plaquette_correlation_data = []
    for j_idx, corr_obj in enumerate(correlation_results):
        r_ref_j = r_vectors[reference_plaq_idx, j_idx]
        
        corr_full_raw_unconnected = corr_obj.mean
        
        # C(r) = (1/4) * <[P_r + P_r^-1][P_0 + P_0^-1]> 
        corr_full_scaled = 0.25 * corr_full_raw_unconnected
        
        # 缩放误差和方差
        error_scaled_real = 0.25 * corr_obj.error_of_mean.real
        error_scaled_imag = 0.25 * corr_obj.error_of_mean.imag
        variance_scaled_real = (0.25**2) * corr_obj.variance.real
        variance_scaled_imag = (0.25**2) * corr_obj.variance.imag

        plaquette_correlation_data.append({
            'plaq_i_idx': reference_plaq_idx, 'plaq_j_idx': j_idx,
            'r_x': r_ref_j[0], 'r_y': r_ref_j[1],
            'corr_full_real': corr_full_scaled.real,
            'corr_full_imag': corr_full_scaled.imag,
            'error': error_scaled_real, 
            'error_imag': error_scaled_imag,
            'variance': variance_scaled_real,
            'variance_imag': variance_scaled_imag
        })

    log_message(log_file, "计算傅里叶变换...")
    r_values = np.array([[data['r_x'], data['r_y']] for data in plaquette_correlation_data])
    corr_full_values = np.array([data['corr_full_real'] + 1j * data['corr_full_imag'] for data in plaquette_correlation_data])

    k_grid = np.stack([kx_grid.flatten(), ky_grid.flatten()], axis=1)
    r_values_jax = jnp.array(r_values)
    corr_full_values_jax = jnp.array(corr_full_values)
    k_grid_jax = jnp.array(k_grid)

    def compute_sf_for_k(k_vec):
        phases = jnp.exp(1j * jnp.dot(r_values_jax, k_vec))
        return jnp.sum(corr_full_values_jax * phases)

    compute_sf_vmap = vmap(compute_sf_for_k)
    sf_values = compute_sf_vmap(k_grid_jax)
    sf_values_2d = sf_values.reshape(n_ky, n_kx)
    plaq_sf_complex = np.array(sf_values_2d)

    N_sites = lattice.n_nodes
    plaq_sf_complex /= N_sites

    # 保存优化的数据结构
    reference_info = {
        'reference_plaquette': {
            'plaq_idx': reference_plaq_idx,
            'sites': plaquettes[reference_plaq_idx]
        },
        'total_plaquettes': len(plaquettes)
    }
    
    optimized_data = create_data_structure(
        correlation_data=plaquette_correlation_data,
        k_points_x=k_points_x,
        k_points_y=k_points_y,
        structure_factor_complex=plaq_sf_complex,
        lattice=lattice,
        calculation_type='plaquette',
        reference_info=reference_info
    )
    save_optimized_data(optimized_data, os.path.join(save_dir, "plaquette_data.npy"))

    log_message(log_file, "简盘结构因子计算完成（使用真正并行采样）")

    return (k_points_x, k_points_y), (plaq_sf_complex.real, plaq_sf_complex.imag)


def analyze_optimized_structure_factor(data_path, analysis_type='basic'):
    """
    分析优化数据结构中的结构因子
    
    参数:
    - data_path: 优化数据文件路径
    - analysis_type: 分析类型 ('basic', 'peaks', 'correlations')
    
    返回:
    - 分析结果字典
    """
    # 加载数据
    data = load_optimized_data(data_path)
    
    # 基本信息
    metadata = data['metadata']
    sf_values = data['structure_factor']['values']
    k_points = data['structure_factor']['k_points']
    correlations = data['correlations']['values']
    
    results = {
        'metadata': metadata,
        'structure_factor_shape': sf_values.shape,
        'max_sf_value': np.max(np.abs(sf_values)),
        'min_sf_value': np.min(np.abs(sf_values))
    }
    
    if analysis_type in ['basic', 'peaks']:
        # 找到峰值位置
        sf_magnitude = np.abs(sf_values)
        peak_idx = np.unravel_index(np.argmax(sf_magnitude), sf_magnitude.shape)
        peak_k = k_points[peak_idx]
        
        results['peak_analysis'] = {
            'peak_position_idx': peak_idx,
            'peak_k_vector': peak_k,
            'peak_value': sf_values[peak_idx],
            'peak_magnitude': sf_magnitude[peak_idx]
        }
    
    if analysis_type in ['basic', 'correlations']:
        # 相关函数统计
        positions = data['correlations']['positions']
        correlation_magnitudes = np.abs(correlations)
        
        results['correlation_analysis'] = {
            'total_correlations': len(correlations),
            'max_correlation': np.max(correlation_magnitudes),
            'min_correlation': np.min(correlation_magnitudes),
            'mean_correlation': np.mean(correlation_magnitudes),
            'std_correlation': np.std(correlation_magnitudes),
            'max_distance': np.max(np.linalg.norm(positions, axis=1))
        }
    
    return results



def plot_structure_factor_from_optimized(data_path, save_path=None):
    """
    从优化数据结构绘制结构因子
    
    参数:
    - data_path: 优化数据文件路径
    - save_path: 图片保存路径（可选）
    
    返回:
    - matplotlib figure对象
    """
    try:
        import matplotlib.pyplot as plt
        
        # 加载数据
        data = load_optimized_data(data_path)
        sf_values = data['structure_factor']['values']
        k_points = data['structure_factor']['k_points']
        metadata = data['metadata']
        
        # 创建图形
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
        
        # 绘制结构因子实部
        im1 = ax1.imshow(sf_values.real, origin='lower', aspect='auto')
        ax1.set_title(f'{metadata["calculation_type"]} Structure Factor (Real)')
        ax1.set_xlabel('kx index')
        ax1.set_ylabel('ky index')
        plt.colorbar(im1, ax=ax1)
        
        # 绘制结构因子幅值
        im2 = ax2.imshow(np.abs(sf_values), origin='lower', aspect='auto')
        ax2.set_title(f'{metadata["calculation_type"]} Structure Factor (Magnitude)')
        ax2.set_xlabel('kx index')
        ax2.set_ylabel('ky index')
        plt.colorbar(im2, ax=ax2)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        return fig
        
    except ImportError:
        print("Warning: matplotlib not available, cannot plot structure factor")
        return None

# 优化的操作符缓存和批量计算模块

class OperatorCache:
    """
    操作符缓存类，避免重复创建相同的操作符
    """
    def __init__(self, hilbert):
        self.hilbert = hilbert
        self._spin_ops = {}  # 缓存自旋操作符
        self._dimer_ops = {}  # 缓存二聚体操作符
        self._exchange_ops = {}  # 缓存交换算符
        
    def get_spin_operators(self, site_idx):
        """获取指定位点的自旋操作符（缓存版本）"""
        if site_idx not in self._spin_ops:
            sx = nk.operator.spin.sigmax(self.hilbert, site_idx) * 0.5
            sy = nk.operator.spin.sigmay(self.hilbert, site_idx) * 0.5
            sz = nk.operator.spin.sigmaz(self.hilbert, site_idx) * 0.5
            # 转换为JAX操作符
            self._spin_ops[site_idx] = (
                sx.to_jax_operator(),
                sy.to_jax_operator(), 
                sz.to_jax_operator()
            )
        return self._spin_ops[site_idx]
    
    def get_dimer_operator(self, site1, site2):
        """获取二聚体操作符 S_i·S_j（缓存版本）"""
        key = tuple(sorted([site1, site2]))
        if key not in self._dimer_ops:
            sx1, sy1, sz1 = self.get_spin_operators(site1)
            sx2, sy2, sz2 = self.get_spin_operators(site2)
            dimer_op = sx1 @ sx2 + sy1 @ sy2 + sz1 @ sz2
            self._dimer_ops[key] = dimer_op
        return self._dimer_ops[key]
    
    def get_exchange_operator(self, site1, site2):
        """获取交换算符 S_{i,j} = 1/2 + 2S_i·S_j（缓存版本）"""
        key = tuple(sorted([site1, site2]))
        if key not in self._exchange_ops:
            dimer_op = self.get_dimer_operator(site1, site2)
            constant_op = nk.operator.LocalOperator(self.hilbert, constant=0.5).to_jax_operator()
            exchange_op = constant_op + 2.0 * dimer_op
            self._exchange_ops[key] = exchange_op
        return self._exchange_ops[key]

def batch_expectation_values(vqs, operators, batch_size=None, log_file=None):
    """
    改进的批量计算操作符期望值函数
    使用共享样本集来计算所有算符，避免重复采样
    
    参数:
    - vqs: 变分量子态
    - operators: 操作符列表
    - batch_size: 批次大小，用于内存管理
    - log_file: 日志文件路径
    
    返回:
    - 期望值列表
    """
    if batch_size is None or len(operators) <= batch_size:
        # 如果操作符数量不多，一次性计算所有
        return batch_expect_with_shared_samples(vqs, operators, log_file=log_file)
    
    # 如果操作符太多，分批处理但每批内共享样本
    results = []
    for i in range(0, len(operators), batch_size):
        batch_ops = operators[i:i+batch_size]
        batch_results = batch_expect_with_shared_samples(vqs, batch_ops, log_file=log_file)
        results.extend(batch_results)
    
    return results

def optimized_plaquette_permutation(hilbert, plaq_sites, op_cache):
    """
    优化的plaquette循环置换操作符构建
    使用操作符缓存避免重复计算
    """
    a, b, c, d = plaq_sites
    
    # 使用缓存的交换算符
    S_ab = op_cache.get_exchange_operator(a, b)
    S_bc = op_cache.get_exchange_operator(b, c) 
    S_cd = op_cache.get_exchange_operator(c, d)
    S_ad = op_cache.get_exchange_operator(a, d)
    S_dc = op_cache.get_exchange_operator(d, c)
    S_cb = op_cache.get_exchange_operator(c, b)
    
    # 构建循环置换
    P = S_ab @ S_bc @ S_cd
    P_inv = S_ad @ S_dc @ S_cb
    
    return P, P_inv

# 真正的并行采样函数，基于NetKet的样本重用机制
def batch_expect_with_shared_samples(vqs, operators, n_samples=None, log_file=None):
    """
    使用NetKet的样本重用机制来批量计算所有算符的期望值
    这实现了真正的并行采样策略，所有算符使用相同的样本集

    基于NetKet官方文档：
    "Notice that if you call multiple times expect, the same set of samples will be used"

    参数:
    - vqs: 变分量子态
    - operators: 算符列表
    - n_samples: 样本数量，如果为None则使用vqs的默认值
    - log_file: 日志文件路径

    返回:
    - 期望值结果列表
    """
    import time
    
    total_operators = len(operators)
    log_message(log_file, f"准备批量计算 {total_operators} 个算符的期望值...")
    
    if n_samples is not None:
        original_n_samples = vqs.n_samples
        vqs.n_samples = n_samples
        log_message(log_file, f"临时设置样本数为: {n_samples}")

    try:
        # 强制生成新的样本集（只需要一次）
        log_message(log_file, f"开始生成共享样本集（样本数: {vqs.n_samples}）...")
        sampling_start = time.time()
        vqs.sample()
        sampling_time = time.time() - sampling_start
        log_message(log_file, f"样本生成完成，耗时: {sampling_time:.3f} 秒")

        # 现在所有算符都将使用相同的样本集进行计算
        log_message(log_file, f"开始计算 {total_operators} 个算符的期望值（使用共享样本）...")
        results = []
        expectation_start = time.time()
        
        # 设置进度报告间隔
        progress_interval = max(1, total_operators // 20)  # 最多报告20次进度
        
        for i, operator in enumerate(operators):
            # 每次调用expect都会重用相同的样本集
            op_start = time.time()
            stats = vqs.expect(operator)
            op_time = time.time() - op_start
            results.append(stats)
            
            # 定期报告进度
            if (i + 1) % progress_interval == 0 or i == 0 or i == total_operators - 1:
                elapsed = time.time() - expectation_start
                avg_time_per_op = elapsed / (i + 1)
                remaining_ops = total_operators - (i + 1)
                eta = remaining_ops * avg_time_per_op
                log_message(log_file, 
                    f"算符计算进度: {i+1}/{total_operators} "
                    f"({(i+1)/total_operators*100:.1f}%), "
                    f"当前算符耗时: {op_time:.3f}s, "
                    f"平均耗时: {avg_time_per_op:.3f}s, "
                    f"预计剩余时间: {eta:.1f}s")

        total_expectation_time = time.time() - expectation_start
        log_message(log_file, 
            f"所有算符期望值计算完成，总耗时: {total_expectation_time:.3f} 秒, "
            f"平均每算符: {total_expectation_time/total_operators:.3f} 秒")

        return results

    finally:
        if n_samples is not None:
            vqs.n_samples = original_n_samples
            log_message(log_file, f"恢复原始样本数: {original_n_samples}")

# 高级并行采样函数：使用SumOperator进行真正的批量计算
def batch_expect_with_sum_operator(vqs, operators, n_samples=None, log_file=None):
    """
    使用NetKet的SumOperator来实现真正的批量并行计算
    这是最高效的方法，因为它在算符级别进行批量处理

    参数:
    - vqs: 变分量子态
    - operators: 算符列表
    - n_samples: 样本数量，如果为None则使用vqs的默认值
    - log_file: 日志文件路径

    返回:
    - 期望值结果列表
    """
    if len(operators) == 1:
        # 如果只有一个算符，直接计算
        return [vqs.expect(operators[0])]

    if n_samples is not None:
        original_n_samples = vqs.n_samples
        vqs.n_samples = n_samples

    try:
        # 创建一个包含所有算符的SumOperator
        # 注意：这里我们不是要计算和，而是要利用SumOperator的批量计算能力
        # 我们将使用权重为1的方式来分别计算每个算符

        # 由于SumOperator计算的是加权和，我们需要用不同的方法
        # 最简单的方法还是使用样本重用机制
        return batch_expect_with_shared_samples(vqs, operators, n_samples=None, log_file=log_file)

    finally:
        if n_samples is not None:
            vqs.n_samples = original_n_samples

# 向量化的期望值计算函数（实验性）
def vectorized_expect_experimental(vqs, operators, n_samples=None, log_file=None):
    """
    实验性的向量化期望值计算
    尝试使用JAX的向量化能力来同时计算多个算符

    注意：这是实验性功能，可能不稳定
    
    参数:
    - log_file: 日志文件路径
    """
    # 目前回退到标准的批量采样方法
    return batch_expect_with_shared_samples(vqs, operators, n_samples, log_file=log_file)

# 性能比较函数
def compare_sampling_strategies(vqs, operators, log_file=None):
    """
    比较不同采样策略的性能

    参数:
    - vqs: 变分量子态
    - operators: 算符列表
    - log_file: 日志文件路径

    返回:
    - 性能比较结果字典
    """
    import time

    if len(operators) < 2:
        log_message(log_file, "算符数量太少，无法进行有意义的性能比较")
        return None

    log_message(log_file, f"开始性能比较测试，算符数量: {len(operators)}")

    # 测试新的并行采样策略
    log_message(log_file, "测试真正的并行采样策略...")
    start_time = time.time()
    results_parallel = batch_expect_with_shared_samples(vqs, operators, log_file=log_file)
    parallel_time = time.time() - start_time

    # 测试传统的串行方法（每个算符单独采样）
    log_message(log_file, "测试传统的串行采样方法...")
    start_time = time.time()
    results_serial = []
    for op in operators:
        vqs.sample()  # 每次都重新采样
        results_serial.append(vqs.expect(op))
    serial_time = time.time() - start_time

    # 计算性能提升
    speedup = serial_time / parallel_time if parallel_time > 0 else float('inf')

    performance_results = {
        'parallel_time': parallel_time,
        'serial_time': serial_time,
        'speedup': speedup,
        'n_operators': len(operators),
        'parallel_time_per_op': parallel_time / len(operators),
        'serial_time_per_op': serial_time / len(operators)
    }

    log_message(log_file, f"性能比较结果:")
    log_message(log_file, f"  并行采样总时间: {parallel_time:.3f} 秒")
    log_message(log_file, f"  串行采样总时间: {serial_time:.3f} 秒")
    log_message(log_file, f"  性能提升倍数: {speedup:.2f}x")
    log_message(log_file, f"  并行采样平均每算符: {parallel_time/len(operators):.3f} 秒")
    log_message(log_file, f"  串行采样平均每算符: {serial_time/len(operators):.3f} 秒")

    return performance_results

# 改进的结构因子计算函数，使用真正的并行采样
def calculate_spin_structure_factor_parallel(vqs, lattice, L, save_dir, log_file=None):
    """
    计算自旋结构因子，使用真正的并行采样优化版本
    一次生成样本，批量计算所有算符期望值
    """
    # Try to determine a specific log_file name from save_dir components
    temp_dir_parts = save_dir.split('/')
    for i, current_dir_part in enumerate(temp_dir_parts):
        if current_dir_part.startswith('J2='):
            if i + 1 < len(temp_dir_parts) and temp_dir_parts[i+1].startswith('J1='):
                temp_J2_str = current_dir_part.split('=')[1]
                temp_J1_str = temp_dir_parts[i+1].split('=')[1]
                log_file = os.path.join(os.path.dirname(save_dir), f"analyze_L={L}_J2={temp_J2_str}_J1={temp_J1_str}.log")
                break

    log_message(log_file, "开始计算自旋结构因子（真正并行采样版本）...")

    N = lattice.n_nodes

    # 创建k点网格
    k_points_x, k_points_y, kx_grid, ky_grid = create_k_mesh(lattice)
    n_kx = len(k_points_x)
    n_ky = len(k_points_y)

    # 初始化操作符缓存
    log_message(log_file, "初始化操作符缓存...")
    op_cache = OperatorCache(vqs.hilbert)

    # 创建位置矩阵，用于向量化计算
    positions = np.array([lattice.positions[i] for i in range(N)])

    # 计算所有位点对之间的位移向量
    log_message(log_file, "计算位移向量...")
    r_vectors = np.zeros((N, N, 2))
    for i in range(N):
        r_vectors[i] = positions - positions[i]

    # 预构建所有自旋点积操作符 S_0·S_j
    log_message(log_file, "预构建所有自旋相关操作符...")
    ops_S0_dot_Sj_list = []
    for j in range(N):
        spin_dot_op = op_cache.get_dimer_operator(0, j)
        ops_S0_dot_Sj_list.append(spin_dot_op)

    # 使用真正的并行采样批量计算（所有算符使用相同样本集）
    import time
    start_time = time.time()

    correlation_results = batch_expect_with_shared_samples(vqs, ops_S0_dot_Sj_list, log_file=log_file)

    end_time = time.time()
    log_message(log_file, f"自旋相关函数计算完成，总耗时 {end_time - start_time:.2f} 秒，平均每个算符 {(end_time - start_time)/len(ops_S0_dot_Sj_list):.3f} 秒")

    # 处理结果
    correlation_data = []
    update_interval = max(1, N // 10)
    
    for j, corr_obj in enumerate(correlation_results):
        r_0j = r_vectors[0, j]
        unconnected_corr_S0_Sj_mean = corr_obj.mean

        corr_full_real = unconnected_corr_S0_Sj_mean.real
        corr_full_imag = unconnected_corr_S0_Sj_mean.imag
        
        current_error_real: float
        current_error_imag: float
        current_variance_real: float
        current_variance_imag: float

        if j == 0: 
            current_error_real = 0.0
            current_error_imag = 0.0
            current_variance_real = 0.0
            current_variance_imag = 0.0
        else:
            current_error_real = corr_obj.error_of_mean.real
            current_error_imag = corr_obj.error_of_mean.imag
            current_variance_real = corr_obj.variance.real
            current_variance_imag = corr_obj.variance.imag

        correlation_data.append({
            'i': 0, 'j': j,
            'r_x': r_0j[0], 'r_y': r_0j[1],
            'corr_full_real': corr_full_real,
            'corr_full_imag': corr_full_imag,
            'error': current_error_real, 
            'error_imag': current_error_imag, 
            'variance': current_variance_real, 
            'variance_imag': current_variance_imag
        })
        if j % update_interval == 0 or j == N - 1:
            log_message(log_file, f"处理相关函数进度: {j+1}/{N}")

    # 向量化计算傅里叶变换
    log_message(log_file, "计算傅里叶变换...")

    # 提取相关数据用于向量化计算
    r_values = np.array([[data['r_x'], data['r_y']] for data in correlation_data])
    corr_full_values = np.array([data['corr_full_real'] + 1j * data['corr_full_imag'] for data in correlation_data])

    # 使用已经创建的k网格
    k_grid = np.stack([kx_grid.flatten(), ky_grid.flatten()], axis=1)

    # 将数据转换为JAX数组
    r_values_jax = jnp.array(r_values)
    corr_full_values_jax = jnp.array(corr_full_values)
    k_grid_jax = jnp.array(k_grid)

    # 定义计算单个k点的结构因子的函数
    def compute_sf_for_k(k_vec):
        phases = jnp.exp(1j * jnp.dot(r_values_jax, k_vec))
        return jnp.sum(corr_full_values_jax * phases)

    # 向量化函数以并行计算所有k点
    compute_sf_vmap = vmap(compute_sf_for_k)

    # 并行计算所有k点的结构因子
    sf_values = compute_sf_vmap(k_grid_jax)

    # 将结果重塑为2D网格
    sf_values_2d = sf_values.reshape(n_ky, n_kx)

    # 存储结果
    spin_sf = np.array(sf_values_2d)

    # 归一化
    spin_sf /= N

    # 保存优化的数据结构
    optimized_data = create_data_structure(
        correlation_data=correlation_data,
        k_points_x=k_points_x,
        k_points_y=k_points_y,
        structure_factor_complex=spin_sf,
        lattice=lattice,
        calculation_type='spin',
        reference_info={'reference_site': 0}
    )
    save_optimized_data(optimized_data, os.path.join(save_dir, "spin_data.npy"))

    log_message(log_file, "自旋结构因子计算完成（使用真正并行采样）")

    return (k_points_x, k_points_y), (spin_sf.real, spin_sf.imag)

def calculate_dimer_structure_factor_parallel(vqs, lattice, L, save_dir, log_file=None):
    """
    计算二聚体-二聚体结构因子，使用真正的并行采样优化版本
    一次生成样本，批量计算所有算符期望值
    """
    # Try to determine a specific log_file name from save_dir components
    temp_dir_parts = save_dir.split('/')
    for i, current_dir_part in enumerate(temp_dir_parts):
        if current_dir_part.startswith('J2='):
            if i + 1 < len(temp_dir_parts) and temp_dir_parts[i+1].startswith('J1='):
                temp_J2_str = current_dir_part.split('=')[1]
                temp_J1_str = temp_dir_parts[i+1].split('=')[1]
                log_file = os.path.join(os.path.dirname(save_dir), f"analyze_L={L}_J2={temp_J2_str}_J1={temp_J1_str}.log")
                break
    
    log_message(log_file, "开始计算二聚体结构因子（真正并行采样版本）...")

    import time
    # 获取总位点数
    N = lattice.n_nodes

    # 创建k点网格
    k_points_x, k_points_y, kx_grid, ky_grid = create_k_mesh(lattice)
    n_kx = len(k_points_x)
    n_ky = len(k_points_y)

    # 收集所有方向的二聚体键（分别处理x和y方向）
    log_message(log_file, "识别x和y方向的二聚体...")
    dimers_x = []  # x方向的二聚体
    dimers_y = []  # y方向的二聚体
    dimer_positions_x = []
    dimer_positions_y = []

    # 预先获取所有边
    edges = list(lattice.edges(color=0))

    for edge in edges:
        site_i, site_j = edge
        if site_i > site_j:
            site_i, site_j = site_j, site_i

        pos_i = np.array(lattice.positions[site_i])
        pos_j = np.array(lattice.positions[site_j])
        displacement = pos_j - pos_i

        # 检查是否为单位长度的x方向dimer
        if abs(abs(displacement[0]) - 1.0) < 0.1 and abs(displacement[1]) < 0.1:
            dimers_x.append((site_i, site_j))
            dimer_center = 0.5 * (pos_i + pos_j)
            dimer_positions_x.append(dimer_center)
        # 检查是否为单位长度的y方向dimer
        elif abs(abs(displacement[1]) - 1.0) < 0.1 and abs(displacement[0]) < 0.1:
            dimers_y.append((site_i, site_j))
            dimer_center = 0.5 * (pos_i + pos_j)
            dimer_positions_y.append(dimer_center)

    dimers = dimers_x + dimers_y
    dimer_positions = dimer_positions_x + dimer_positions_y
    n_dimers_x = len(dimers_x)
    n_dimers_y = len(dimers_y)

    if len(dimers) == 0:
        log_message(log_file, "警告: 没有找到二聚体！")
        return (k_points_x, k_points_y), (np.zeros((n_ky, n_kx)), np.zeros((n_ky, n_kx)))

    n_dimers = len(dimers)

    log_message(log_file, "预计算自旋操作符...")
    spin_ops_components = []
    for i in range(lattice.n_nodes):
        sx_i = nk.operator.spin.sigmax(vqs.hilbert, i) * 0.5
        sy_i = nk.operator.spin.sigmay(vqs.hilbert, i) * 0.5
        sz_i = nk.operator.spin.sigmaz(vqs.hilbert, i) * 0.5
        sx_i_jax = sx_i.to_jax_operator()
        sy_i_jax = sy_i.to_jax_operator()
        sz_i_jax = sz_i.to_jax_operator()
        spin_ops_components.append((sx_i_jax, sy_i_jax, sz_i_jax))

    log_message(log_file, "预计算二聚体操作符 D_k = S_k1·S_k2 ...")
    dimer_ops_list = []
    for i, (i1, i2) in enumerate(dimers):
        sx_i1, sy_i1, sz_i1 = spin_ops_components[i1]
        sx_i2, sy_i2, sz_i2 = spin_ops_components[i2]
        S_i1_dot_S_i2 = sx_i1 @ sx_i2 + sy_i1 @ sy_i2 + sz_i1 @ sz_i2
        dimer_ops_list.append(S_i1_dot_S_i2)

    log_message(log_file, "计算位移向量...")
    dimer_positions_np = np.array(dimer_positions)
    r_vectors = np.zeros((n_dimers, n_dimers, 2))
    for i in range(n_dimers):
        r_vectors[i] = dimer_positions_np - dimer_positions_np[i]

    dimer_correlation_data = []

    # 批量构建所有需要计算的操作符
    all_operators = []
    operator_info = []  # 存储操作符的元信息
    
    if n_dimers_x > 0:
        ref_dimer_idx_x = 0
        op_D_ref_x = dimer_ops_list[ref_dimer_idx_x]
        dimer_ref_tuple_x = dimers[ref_dimer_idx_x]
        log_message(log_file, f"准备x方向二聚体相关函数计算...")
        
        for j_local_idx in range(n_dimers_x):
            j_global_idx = j_local_idx
            op_Dj = dimer_ops_list[j_global_idx]
            combined_op = op_D_ref_x @ op_Dj
            all_operators.append(combined_op)
            operator_info.append({
                'type': 'x_dimer',
                'ref_idx': ref_dimer_idx_x,
                'j_idx': j_global_idx,
                'ref_tuple': dimer_ref_tuple_x,
                'j_tuple': dimers[j_global_idx]
            })

    if n_dimers_y > 0:
        ref_dimer_idx_y = n_dimers_x
        op_D_ref_y = dimer_ops_list[ref_dimer_idx_y]
        dimer_ref_tuple_y = dimers[ref_dimer_idx_y]
        log_message(log_file, f"准备y方向二聚体相关函数计算...")

        for j_local_idx in range(n_dimers_y):
            j_global_idx = n_dimers_x + j_local_idx
            op_Dj = dimer_ops_list[j_global_idx]
            combined_op = op_D_ref_y @ op_Dj
            all_operators.append(combined_op)
            operator_info.append({
                'type': 'y_dimer',
                'ref_idx': ref_dimer_idx_y,
                'j_idx': j_global_idx,
                'ref_tuple': dimer_ref_tuple_y,
                'j_tuple': dimers[j_global_idx]
            })

    # 使用真正的并行采样批量计算所有操作符
    log_message(log_file, f"使用真正的并行采样批量计算 {len(all_operators)} 个二聚体相关函数...")
    start_time = time.time()

    correlation_results = batch_expect_with_shared_samples(vqs, all_operators, log_file=log_file)

    end_time = time.time()
    log_message(log_file, f"二聚体相关函数计算完成，总耗时 {end_time - start_time:.2f} 秒，平均每个算符 {(end_time - start_time)/len(all_operators):.3f} 秒")

    # 处理结果
    for idx, (corr_obj, info) in enumerate(zip(correlation_results, operator_info)):
        r_ref_j = r_vectors[info['ref_idx'], info['j_idx']]
        corr_full = corr_obj.mean

        direction = 'x' if info['type'] == 'x_dimer' else 'y'

        dimer_correlation_data.append({
            'dimer_i_idx': info['ref_idx'],
            'dimer_j_idx': info['j_idx'],
            'dimer_i_sites': info['ref_tuple'],
            'dimer_j_sites': info['j_tuple'],
            'r_x': r_ref_j[0],
            'r_y': r_ref_j[1],
            'corr_full_real': corr_full.real,
            'corr_full_imag': corr_full.imag,
            'error': corr_obj.error_of_mean.real,
            'error_imag': corr_obj.error_of_mean.imag,
            'variance': corr_obj.variance.real,
            'variance_imag': corr_obj.variance.imag,
            'direction': direction
        })

    if not dimer_correlation_data:
        log_message(log_file, "没有足够的数据点进行傅里叶变换。")
        return (k_points_x, k_points_y), (np.zeros((n_ky, n_kx)), np.zeros((n_ky, n_kx)))

    log_message(log_file, "计算傅里叶变换...")

    r_values = np.array([[data['r_x'], data['r_y']] for data in dimer_correlation_data])
    corr_full_values = np.array([data['corr_full_real'] + 1j * data['corr_full_imag'] for data in dimer_correlation_data])

    k_grid = np.stack([kx_grid.flatten(), ky_grid.flatten()], axis=1)
    r_values_jax = jnp.array(r_values)
    corr_full_values_jax = jnp.array(corr_full_values)
    k_grid_jax = jnp.array(k_grid)

    def compute_sf_for_k(k_vec):
        phases = jnp.exp(1j * jnp.dot(r_values_jax, k_vec))
        return jnp.sum(corr_full_values_jax * phases)

    compute_sf_vmap = vmap(compute_sf_for_k)
    sf_values = compute_sf_vmap(k_grid_jax)
    sf_values_2d = sf_values.reshape(n_ky, n_kx)
    dimer_sf_complex = np.array(sf_values_2d)

    dimer_sf_complex /= N

    # 保存优化的数据结构
    reference_info = {}
    if n_dimers_x > 0:
        reference_info['x_reference'] = {'dimer_idx': 0, 'sites': dimers[0]}
    if n_dimers_y > 0:
        reference_info['y_reference'] = {'dimer_idx': n_dimers_x, 'sites': dimers[n_dimers_x]}

    optimized_data = create_data_structure(
        correlation_data=dimer_correlation_data,
        k_points_x=k_points_x,
        k_points_y=k_points_y,
        structure_factor_complex=dimer_sf_complex,
        lattice=lattice,
        calculation_type='dimer',
        reference_info=reference_info
    )
    save_optimized_data(optimized_data, os.path.join(save_dir, "dimer_data.npy"))

    log_message(log_file, "二聚体结构因子计算完成（使用真正并行采样）")

    return (k_points_x, k_points_y), (dimer_sf_complex.real, dimer_sf_complex.imag)




